package main

import (
	"fmt"
	"log"
	"os"
	"strings"

	"github.com/nats-io/nats.go"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/config"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/initializer"
	natsClient "gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/nats"
	natsModel "gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/nats"
)

func main() {
	// Initialize configuration
	global.GVA_VP = initializer.Viper()
	global.GVA_LOG = initializer.Zap()

	if global.GVA_CONFIG.NatsMeme.URL == "" {
		log.Fatal("NATS Meme URL not configured")
	}

	// Initialize NATS client
	natsClientInstance := natsClient.InitNatsJetStream(global.GVA_CONFIG.NatsMeme)
	defer natsClientInstance.Close()

	fmt.Println("Connected to NATS, listing consumers...")

	// List all consumers for the affiliate stream
	consumers := natsClientInstance.ListConsumers(natsModel.AffiliateStream)
	
	var orphanedConsumers []string
	var validConsumers []string

	for consumer := range consumers {
		if consumer == nil {
			continue
		}

		consumerName := consumer.Name
		
		// Check if this is an orphaned consumer (has timestamp suffix)
		if strings.HasPrefix(consumerName, natsModel.AffiliateTxConsumer+"-") ||
		   strings.HasPrefix(consumerName, natsModel.SolPriceConsumer+"-") {
			orphanedConsumers = append(orphanedConsumers, consumerName)
		} else {
			validConsumers = append(validConsumers, consumerName)
		}
	}

	fmt.Printf("Found %d valid consumers:\n", len(validConsumers))
	for _, name := range validConsumers {
		fmt.Printf("  - %s\n", name)
	}

	fmt.Printf("\nFound %d orphaned consumers:\n", len(orphanedConsumers))
	for _, name := range orphanedConsumers {
		fmt.Printf("  - %s\n", name)
	}

	if len(orphanedConsumers) == 0 {
		fmt.Println("\nNo orphaned consumers to clean up!")
		return
	}

	// Ask for confirmation
	fmt.Print("\nDo you want to delete all orphaned consumers? (y/N): ")
	var response string
	fmt.Scanln(&response)

	if strings.ToLower(response) != "y" && strings.ToLower(response) != "yes" {
		fmt.Println("Cleanup cancelled.")
		return
	}

	// Delete orphaned consumers
	fmt.Println("\nDeleting orphaned consumers...")
	for _, consumerName := range orphanedConsumers {
		fmt.Printf("Deleting consumer: %s... ", consumerName)
		
		if err := natsClientInstance.DeleteConsumer(natsModel.AffiliateStream, consumerName); err != nil {
			fmt.Printf("FAILED: %v\n", err)
		} else {
			fmt.Println("SUCCESS")
		}
	}

	fmt.Println("\nCleanup completed!")
}
