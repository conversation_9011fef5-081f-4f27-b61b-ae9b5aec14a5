package nats

import "github.com/nats-io/nats.go"

type Subscriber interface {
	Subscribe(subject string, handler nats.MsgHandler) (*nats.Subscription, error)

	SubscribeJS(subject string, handler nats.MsgHandler, opts ...nats.SubOpt) (*nats.Subscription, error)

	AddStream(cfg *nats.StreamConfig) (*nats.StreamInfo, error)

	DeleteConsumer(streamName, consumerName string) error

	ConsumerInfo(streamName, consumerName string) (*nats.ConsumerInfo, error)

	ListConsumers(streamName string) <-chan *nats.ConsumerInfo
}
