package affiliate

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/nats-io/nats.go"
	"go.uber.org/zap"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	natsClient "gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/nats"
	natsModel "gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/nats"
)

// AffiliateSubscriberService handles NATS subscription for affiliate transactions and SOL price updates
type AffiliateSubscriberService struct {
	natsClient       natsClient.Subscriber
	affiliateService AffiliateServiceInterface
}

// AffiliateServiceInterface defines the interface for affiliate service operations
type AffiliateServiceInterface interface {
	ProcessAffiliateTransaction(ctx context.Context, txEvent *natsModel.AffiliateTxEvent) error
	ProcessSolPriceUpdate(ctx context.Context, priceEvent *natsModel.SolPriceEvent) error
}

// NewAffiliateSubscriberService creates a new affiliate subscriber service
func NewAffiliateSubscriberService(natsClient natsClient.Subscriber, affiliateService AffiliateServiceInterface) *AffiliateSubscriberService {
	return &AffiliateSubscriberService{
		natsClient:       natsClient,
		affiliateService: affiliateService,
	}
}

// Start begins listening to NATS streams for affiliate transactions and SOL price updates
func (s *AffiliateSubscriberService) Start(ctx context.Context) error {
	global.GVA_LOG.Info("Starting AffiliateSubscriberService")

	// Create or ensure the affiliate stream exists
	_, err := s.natsClient.AddStream(&nats.StreamConfig{
		Name:     natsModel.AffiliateStream,
		Subjects: []string{natsModel.AffiliateTxSubject, natsModel.SolPriceSubject},
		Storage:  nats.FileStorage,
	})
	if err != nil {
		return fmt.Errorf("failed to create affiliate stream: %w", err)
	}

	// Clean up any orphaned consumers before starting
	s.cleanupOrphanedConsumers()

	// Start affiliate transaction subscriber
	if err := s.startAffiliateTxSubscriber(ctx); err != nil {
		return fmt.Errorf("failed to start affiliate transaction subscriber: %w", err)
	}

	// Start SOL price subscriber
	if err := s.startSolPriceSubscriber(ctx); err != nil {
		return fmt.Errorf("failed to start SOL price subscriber: %w", err)
	}

	global.GVA_LOG.Info("AffiliateSubscriberService started successfully")
	return nil
}

// startAffiliateTxSubscriber starts the subscriber for affiliate transactions
func (s *AffiliateSubscriberService) startAffiliateTxSubscriber(ctx context.Context) error {
	global.GVA_LOG.Info("Starting affiliate transaction subscriber",
		zap.String("subject", natsModel.AffiliateTxSubject))

	// Try with original consumer name first
	consumerName := natsModel.AffiliateTxConsumer
	sub, err := s.natsClient.SubscribeJS(natsModel.AffiliateTxSubject, func(msg *nats.Msg) {
		s.handleAffiliateTxMessage(ctx, msg)
	},
		nats.Durable(consumerName),
		nats.ManualAck(),
		nats.BindStream(natsModel.AffiliateStream),
		nats.AckWait(30*time.Second),
	)

	if err != nil {
		// Check if it's a "consumer already bound" error
		if strings.Contains(err.Error(), "consumer is already bound to a subscription") {
			global.GVA_LOG.Warn("Consumer already exists, attempting to delete and recreate",
				zap.String("consumer", consumerName),
				zap.Error(err))

			// Try to delete the existing consumer
			if deleteErr := s.natsClient.DeleteConsumer(natsModel.AffiliateStream, consumerName); deleteErr != nil {
				global.GVA_LOG.Warn("Failed to delete existing consumer, will continue anyway",
					zap.String("consumer", consumerName),
					zap.Error(deleteErr))
			} else {
				global.GVA_LOG.Info("Successfully deleted existing consumer",
					zap.String("consumer", consumerName))
			}

			// Wait a moment for the deletion to propagate
			time.Sleep(100 * time.Millisecond)

			// Retry subscription with original consumer name
			sub, err = s.natsClient.SubscribeJS(natsModel.AffiliateTxSubject, func(msg *nats.Msg) {
				s.handleAffiliateTxMessage(ctx, msg)
			},
				nats.Durable(consumerName),
				nats.ManualAck(),
				nats.BindStream(natsModel.AffiliateStream),
				nats.AckWait(30*time.Second),
			)

			if err != nil {
				return fmt.Errorf("failed to subscribe to affiliate transaction subject after consumer recreation: %w", err)
			}

			global.GVA_LOG.Info("Successfully subscribed after consumer recreation",
				zap.String("consumer", consumerName))
		} else {
			return fmt.Errorf("failed to subscribe to affiliate transaction subject: %w", err)
		}
	}

	// Handle graceful shutdown
	go func() {
		<-ctx.Done()
		global.GVA_LOG.Info("Unsubscribing from affiliate transaction subject")
		if err := sub.Unsubscribe(); err != nil {
			global.GVA_LOG.Error("Failed to unsubscribe from affiliate transaction subject", zap.Error(err))
		}
	}()

	return nil
}

// startSolPriceSubscriber starts the subscriber for SOL price updates
func (s *AffiliateSubscriberService) startSolPriceSubscriber(ctx context.Context) error {
	global.GVA_LOG.Info("Starting SOL price subscriber",
		zap.String("subject", natsModel.SolPriceSubject))

	// Try with original consumer name first
	consumerName := natsModel.SolPriceConsumer
	sub, err := s.natsClient.SubscribeJS(natsModel.SolPriceSubject, func(msg *nats.Msg) {
		s.handleSolPriceMessage(ctx, msg)
	},
		nats.Durable(consumerName),
		nats.ManualAck(),
		nats.BindStream(natsModel.AffiliateStream),
		nats.AckWait(30*time.Second),
	)

	if err != nil {
		// Check if it's a "consumer already bound" error
		if strings.Contains(err.Error(), "consumer is already bound to a subscription") {
			global.GVA_LOG.Warn("SOL price consumer already exists, attempting to delete and recreate",
				zap.String("consumer", consumerName),
				zap.Error(err))

			// Try to delete the existing consumer
			if deleteErr := s.natsClient.DeleteConsumer(natsModel.AffiliateStream, consumerName); deleteErr != nil {
				global.GVA_LOG.Warn("Failed to delete existing SOL price consumer, will continue anyway",
					zap.String("consumer", consumerName),
					zap.Error(deleteErr))
			} else {
				global.GVA_LOG.Info("Successfully deleted existing SOL price consumer",
					zap.String("consumer", consumerName))
			}

			// Wait a moment for the deletion to propagate
			time.Sleep(100 * time.Millisecond)

			// Retry subscription with original consumer name
			sub, err = s.natsClient.SubscribeJS(natsModel.SolPriceSubject, func(msg *nats.Msg) {
				s.handleSolPriceMessage(ctx, msg)
			},
				nats.Durable(consumerName),
				nats.ManualAck(),
				nats.BindStream(natsModel.AffiliateStream),
				nats.AckWait(30*time.Second),
			)

			if err != nil {
				return fmt.Errorf("failed to subscribe to SOL price subject after consumer recreation: %w", err)
			}

			global.GVA_LOG.Info("Successfully subscribed to SOL price after consumer recreation",
				zap.String("consumer", consumerName))
		} else {
			return fmt.Errorf("failed to subscribe to SOL price subject: %w", err)
		}
	}

	// Handle graceful shutdown
	go func() {
		<-ctx.Done()
		global.GVA_LOG.Info("Unsubscribing from SOL price subject")
		if err := sub.Unsubscribe(); err != nil {
			global.GVA_LOG.Error("Failed to unsubscribe from SOL price subject", zap.Error(err))
		}
	}()

	return nil
}

// handleAffiliateTxMessage processes affiliate transaction messages from NATS
func (s *AffiliateSubscriberService) handleAffiliateTxMessage(ctx context.Context, msg *nats.Msg) {
	// Log raw message for debugging
	dataPreview := string(msg.Data)
	if len(dataPreview) > 500 {
		dataPreview = dataPreview[:500] + "..."
	}
	global.GVA_LOG.Debug("Received raw affiliate transaction message",
		zap.String("subject", msg.Subject),
		zap.Int("data_size", len(msg.Data)),
		zap.String("data_preview", dataPreview))

	var wrapper natsModel.AffiliateTxEventWrapper
	if err := json.Unmarshal(msg.Data, &wrapper); err != nil {
		global.GVA_LOG.Error("Failed to unmarshal affiliate transaction message",
			zap.Error(err),
			zap.String("data", string(msg.Data)))
		msg.Ack()
		return
	}

	// Log the number of items received
	global.GVA_LOG.Info("Received affiliate transaction batch",
		zap.Int("item_count", len(wrapper.Items)))

	// Process each transaction in the items array
	for i, txEvent := range wrapper.Items {
		global.GVA_LOG.Info("Processing affiliate transaction",
			zap.Int("item_index", i),
			zap.String("order_id", txEvent.ID.String()),
			zap.String("user_id", txEvent.UserId),
			zap.String("wallet_address", txEvent.UserAddress),
			zap.String("transaction_type", string(txEvent.TransactionType)),
			zap.String("status", string(txEvent.Status)),
			zap.String("base_symbol", txEvent.BaseSymbol),
			zap.String("quote_symbol", txEvent.QuoteSymbol))

		// Skip processing if essential fields are missing
		if txEvent.UserId == "" {
			global.GVA_LOG.Warn("Skipping transaction with empty user_id",
				zap.String("order_id", txEvent.ID.String()))
			continue
		}

		// Process the transaction
		if err := s.affiliateService.ProcessAffiliateTransaction(ctx, &txEvent); err != nil {
			global.GVA_LOG.Error("Failed to process affiliate transaction",
				zap.Error(err),
				zap.String("order_id", txEvent.ID.String()),
				zap.String("user_id", txEvent.UserId))
			// Continue processing other transactions in the batch
			continue
		}

		global.GVA_LOG.Debug("Successfully processed affiliate transaction",
			zap.String("order_id", txEvent.ID.String()),
			zap.String("user_id", txEvent.UserId))
	}

	// Acknowledge the message
	msg.Ack()
}

// handleSolPriceMessage processes SOL price update messages from NATS
func (s *AffiliateSubscriberService) handleSolPriceMessage(ctx context.Context, msg *nats.Msg) {
	var priceEvent natsModel.SolPriceEvent
	if err := json.Unmarshal(msg.Data, &priceEvent); err != nil {
		global.GVA_LOG.Error("Failed to unmarshal SOL price message",
			zap.Error(err),
			zap.String("data", string(msg.Data)))
		msg.Ack()
		return
	}

	global.GVA_LOG.Debug("Received SOL price update",
		zap.String("symbol", priceEvent.GetSymbol()),
		zap.String("price", priceEvent.UsdPrice.String()),
		zap.Time("timestamp", priceEvent.GetTime()))

	// Process the price update
	if err := s.affiliateService.ProcessSolPriceUpdate(ctx, &priceEvent); err != nil {
		global.GVA_LOG.Error("Failed to process SOL price update",
			zap.Error(err),
			zap.String("symbol", priceEvent.GetSymbol()))
		// Don't ack the message so it will be redelivered
		return
	}

	// Acknowledge the message
	msg.Ack()
}

// cleanupOrphanedConsumers removes any consumers with timestamp suffixes that may have been created
// by previous instances of the application
func (s *AffiliateSubscriberService) cleanupOrphanedConsumers() {
	global.GVA_LOG.Info("Cleaning up orphaned consumers")

	// Get list of all consumers for the affiliate stream
	consumers := s.natsClient.ListConsumers(natsModel.AffiliateStream)

	for consumer := range consumers {
		if consumer == nil {
			continue
		}

		consumerName := consumer.Name

		// Check if this is an orphaned consumer (has timestamp suffix)
		if strings.HasPrefix(consumerName, natsModel.AffiliateTxConsumer+"-") ||
			strings.HasPrefix(consumerName, natsModel.SolPriceConsumer+"-") {

			global.GVA_LOG.Info("Found orphaned consumer, attempting to delete",
				zap.String("consumer", consumerName))

			if err := s.natsClient.DeleteConsumer(natsModel.AffiliateStream, consumerName); err != nil {
				global.GVA_LOG.Warn("Failed to delete orphaned consumer",
					zap.String("consumer", consumerName),
					zap.Error(err))
			} else {
				global.GVA_LOG.Info("Successfully deleted orphaned consumer",
					zap.String("consumer", consumerName))
			}
		}
	}
}
